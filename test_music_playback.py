#!/usr/bin/env python3
"""
语音控制音乐播放功能测试脚本
用于测试ESP32客户端与WebSocket服务器的音乐播放交互
"""

import asyncio
import websockets
import json
import time
import sys

class MusicPlaybackTester:
    def __init__(self, server_url="ws://192.168.3.17:8768"):
        self.server_url = server_url
        self.websocket = None
        self.connected = False
        
    async def connect(self):
        """连接到WebSocket服务器"""
        try:
            print(f"正在连接到服务器: {self.server_url}")
            self.websocket = await websockets.connect(self.server_url)
            self.connected = True
            print("✓ 连接成功")
            return True
        except Exception as e:
            print(f"✗ 连接失败: {e}")
            return False
    
    async def disconnect(self):
        """断开连接"""
        if self.websocket:
            await self.websocket.close()
            self.connected = False
            print("连接已断开")
    
    async def send_music_request(self):
        """发送音乐播放请求（模拟ESP32语音指令）"""
        if not self.connected:
            print("✗ 未连接到服务器")
            return False
        
        # 构造音乐播放请求JSON（与ESP32发送的格式一致）
        music_request = {
            "type": "command",
            "data": {
                "cmd": "play_music",
                "action": "start",
                "sessionId": str(int(time.time())),
                "timestamp": int(time.time())
            }
        }
        
        try:
            print("发送音乐播放请求...")
            print(f"请求内容: {json.dumps(music_request, indent=2)}")
            
            await self.websocket.send(json.dumps(music_request))
            print("✓ 音乐播放请求已发送")
            return True
        except Exception as e:
            print(f"✗ 发送请求失败: {e}")
            return False
    
    async def listen_for_responses(self, timeout=10):
        """监听服务器响应"""
        if not self.connected:
            print("✗ 未连接到服务器")
            return
        
        print(f"监听服务器响应（超时: {timeout}秒）...")
        
        try:
            # 设置超时
            response = await asyncio.wait_for(
                self.websocket.recv(), 
                timeout=timeout
            )
            
            print("收到服务器响应:")
            
            # 尝试解析JSON响应
            try:
                response_data = json.loads(response)
                print(json.dumps(response_data, indent=2, ensure_ascii=False))
                
                # 检查响应类型
                if response_data.get("type") == "command":
                    cmd = response_data.get("data", {}).get("cmd", "")
                    if cmd == "music_ready":
                        print("✓ 服务器确认音乐流准备就绪")
                        return "music_ready"
                    elif cmd == "music_error":
                        print("✗ 服务器报告音乐播放错误")
                        return "music_error"
                elif response_data.get("type") == "notification":
                    print("ℹ 收到服务器通知消息")
                    return "notification"
                else:
                    print("ℹ 收到其他类型响应")
                    return "other"
                    
            except json.JSONDecodeError:
                print("收到非JSON响应（可能是二进制音频数据）:")
                print(f"数据长度: {len(response)} 字节")
                if len(response) > 8:
                    # 检查是否是音频包头部
                    header = response[:8]
                    print(f"头部数据: {header.hex()}")
                return "binary"
                
        except asyncio.TimeoutError:
            print(f"✗ 等待响应超时（{timeout}秒）")
            return "timeout"
        except Exception as e:
            print(f"✗ 接收响应时出错: {e}")
            return "error"
    
    async def test_music_playback_flow(self):
        """测试完整的音乐播放流程"""
        print("=" * 60)
        print("开始测试语音控制音乐播放功能")
        print("=" * 60)
        
        # 1. 连接到服务器
        print("\n1. 连接测试")
        if not await self.connect():
            return False
        
        # 2. 等待欢迎消息
        print("\n2. 等待欢迎消息")
        welcome_response = await self.listen_for_responses(timeout=5)
        
        # 3. 发送音乐播放请求
        print("\n3. 发送音乐播放请求")
        if not await self.send_music_request():
            return False
        
        # 4. 等待服务器响应
        print("\n4. 等待服务器响应")
        music_response = await self.listen_for_responses(timeout=10)
        
        # 5. 检查是否收到音频数据
        if music_response == "music_ready":
            print("\n5. 等待音频数据")
            audio_response = await self.listen_for_responses(timeout=5)
            if audio_response == "binary":
                print("✓ 收到音频数据，音乐播放功能正常")
            else:
                print("⚠ 未收到音频数据")
        
        # 6. 断开连接
        print("\n6. 断开连接")
        await self.disconnect()
        
        print("\n" + "=" * 60)
        print("测试完成")
        print("=" * 60)
        
        return True

async def main():
    """主函数"""
    print("语音控制音乐播放功能测试工具")
    print("用于测试ESP32与WebSocket服务器的音乐播放交互")
    print()
    
    # 检查命令行参数
    server_url = "ws://192.168.3.17:8768"
    if len(sys.argv) > 1:
        server_url = sys.argv[1]
    
    print(f"目标服务器: {server_url}")
    print("请确保WebSocket音频服务器已启动并在audio/文件夹中放置了WAV文件")
    print()
    
    # 创建测试器
    tester = MusicPlaybackTester(server_url)
    
    try:
        # 运行测试
        await tester.test_music_playback_flow()
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        await tester.disconnect()
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
        await tester.disconnect()

if __name__ == "__main__":
    # 检查依赖
    try:
        import websockets
    except ImportError:
        print("错误: 缺少websockets库")
        print("请运行: pip install websockets")
        sys.exit(1)
    
    # 显示使用说明
    if len(sys.argv) > 2 or (len(sys.argv) == 2 and sys.argv[1] in ["-h", "--help"]):
        print("使用方法:")
        print("  python3 test_music_playback.py [WebSocket服务器URL]")
        print("")
        print("示例:")
        print("  python3 test_music_playback.py")
        print("  python3 test_music_playback.py ws://*************:8768")
        print("")
        sys.exit(0)
    
    # 运行测试
    asyncio.run(main())
