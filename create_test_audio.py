#!/usr/bin/env python3
"""
创建测试音频文件
生成简单的测试音频WAV文件用于音乐播放测试
"""

import numpy as np
import wave
import os

def create_test_audio():
    """创建测试音频文件"""
    
    # 确保audio目录存在
    audio_dir = "audio"
    if not os.path.exists(audio_dir):
        os.makedirs(audio_dir)
        print(f"创建音频目录: {audio_dir}")
    
    # 音频参数
    sample_rate = 16000  # 16kHz采样率
    duration = 10        # 10秒时长
    frequency = 440      # 440Hz (A4音符)
    
    # 生成时间轴
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    
    # 生成正弦波音频数据
    audio_data = np.sin(2 * np.pi * frequency * t)
    
    # 添加音量渐变效果
    fade_samples = int(0.5 * sample_rate)  # 0.5秒渐变
    
    # 渐入
    audio_data[:fade_samples] *= np.linspace(0, 1, fade_samples)
    # 渐出
    audio_data[-fade_samples:] *= np.linspace(1, 0, fade_samples)
    
    # 转换为16位整数
    audio_data = (audio_data * 32767).astype(np.int16)
    
    # 保存为WAV文件
    wav_file = os.path.join(audio_dir, "test_music.wav")
    
    with wave.open(wav_file, 'w') as wav:
        wav.setnchannels(1)        # 单声道
        wav.setsampwidth(2)        # 16位
        wav.setframerate(sample_rate)  # 16kHz
        wav.writeframes(audio_data.tobytes())
    
    print(f"✅ 测试音频文件已创建: {wav_file}")
    print(f"📊 文件信息:")
    print(f"   - 采样率: {sample_rate} Hz")
    print(f"   - 时长: {duration} 秒")
    print(f"   - 频率: {frequency} Hz")
    print(f"   - 声道: 单声道")
    print(f"   - 位深: 16位")
    
    return wav_file

def create_multiple_test_files():
    """创建多个测试音频文件"""
    
    audio_dir = "audio"
    if not os.path.exists(audio_dir):
        os.makedirs(audio_dir)
    
    # 创建不同频率的测试音频
    frequencies = [
        (220, "test_low.wav"),      # A3
        (440, "test_mid.wav"),      # A4  
        (880, "test_high.wav"),     # A5
    ]
    
    sample_rate = 16000
    duration = 5  # 5秒
    
    created_files = []
    
    for freq, filename in frequencies:
        # 生成音频数据
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        audio_data = np.sin(2 * np.pi * freq * t)
        
        # 添加渐变
        fade_samples = int(0.2 * sample_rate)
        audio_data[:fade_samples] *= np.linspace(0, 1, fade_samples)
        audio_data[-fade_samples:] *= np.linspace(1, 0, fade_samples)
        
        # 转换为16位整数
        audio_data = (audio_data * 32767 * 0.7).astype(np.int16)  # 降低音量避免失真
        
        # 保存文件
        wav_file = os.path.join(audio_dir, filename)
        with wave.open(wav_file, 'w') as wav:
            wav.setnchannels(1)
            wav.setsampwidth(2)
            wav.setframerate(sample_rate)
            wav.writeframes(audio_data.tobytes())
        
        created_files.append(wav_file)
        print(f"✅ 创建测试文件: {wav_file} ({freq}Hz)")
    
    return created_files

def main():
    """主函数"""
    print("创建测试音频文件工具")
    print("=" * 40)
    
    try:
        # 检查numpy是否可用
        import numpy as np
        print("✅ NumPy 可用")
    except ImportError:
        print("❌ 错误: 需要安装 numpy")
        print("请运行: pip install numpy")
        return
    
    try:
        # 创建单个测试文件
        print("\n1. 创建主测试音频文件...")
        main_file = create_test_audio()
        
        # 创建多个测试文件
        print("\n2. 创建多个测试音频文件...")
        test_files = create_multiple_test_files()
        
        print(f"\n🎉 完成! 共创建了 {1 + len(test_files)} 个测试音频文件")
        print("\n📁 audio/ 目录内容:")
        
        audio_dir = "audio"
        if os.path.exists(audio_dir):
            for file in os.listdir(audio_dir):
                if file.endswith('.wav'):
                    file_path = os.path.join(audio_dir, file)
                    file_size = os.path.getsize(file_path)
                    print(f"   - {file} ({file_size} 字节)")
        
        print(f"\n💡 现在可以启动WebSocket服务器测试音乐播放功能:")
        print(f"   python3 websocket_audio_server.py")
        
    except Exception as e:
        print(f"❌ 创建音频文件时出错: {e}")

if __name__ == "__main__":
    main()
