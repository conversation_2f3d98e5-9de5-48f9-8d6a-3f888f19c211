#!/usr/bin/env python3
"""
WebSocket连接调试工具
用于诊断ESP32与服务器之间的连接和消息传输问题
"""

import asyncio
import websockets
import json
import time
import sys

class WebSocketDebugger:
    def __init__(self, server_url="ws://192.168.3.17:8768"):
        self.server_url = server_url
        self.websocket = None
        self.connected = False
        self.message_count = 0
        
    async def connect_and_monitor(self):
        """连接到服务器并监控所有消息"""
        try:
            print(f"连接到服务器: {self.server_url}")
            self.websocket = await websockets.connect(self.server_url)
            self.connected = True
            print("✓ 连接成功，开始监控消息...")
            
            # 监听所有消息
            async for message in self.websocket:
                self.message_count += 1
                timestamp = time.strftime('%H:%M:%S')
                
                print(f"\n[{timestamp}] 收到消息 #{self.message_count}")
                print("-" * 50)
                
                # 尝试解析JSON
                try:
                    if isinstance(message, str):
                        # 文本消息
                        print("消息类型: 文本 (JSON)")
                        data = json.loads(message)
                        print("JSON内容:")
                        print(json.dumps(data, indent=2, ensure_ascii=False))
                        
                        # 检查是否是音乐相关指令
                        if self.is_music_command(data):
                            print("🎵 检测到音乐相关指令!")
                            await self.handle_music_command(data)
                            
                    else:
                        # 二进制消息
                        print("消息类型: 二进制")
                        print(f"数据长度: {len(message)} 字节")
                        if len(message) >= 8:
                            # 解析音频包头部
                            header = message[:8]
                            print(f"头部数据: {header.hex()}")
                            
                            # 解析WebSocket音频包格式
                            if len(header) >= 8:
                                version = header[0]
                                audio_type = header[1] 
                                seq_num = int.from_bytes(header[2:4], 'little')
                                payload_len = int.from_bytes(header[4:6], 'little')
                                reserved = int.from_bytes(header[6:8], 'little')
                                
                                print(f"  版本: {version}")
                                print(f"  类型: {audio_type}")
                                print(f"  序列号: {seq_num}")
                                print(f"  负载长度: {payload_len}")
                                print(f"  保留字段: {reserved}")
                                
                                if audio_type == 1:
                                    print("🎵 检测到音频数据包!")
                        
                except json.JSONDecodeError:
                    print("消息类型: 文本 (非JSON)")
                    print(f"内容: {message}")
                except Exception as e:
                    print(f"解析消息时出错: {e}")
                
                print("-" * 50)
                
        except websockets.exceptions.ConnectionClosed:
            print("连接已关闭")
        except Exception as e:
            print(f"连接错误: {e}")
        finally:
            self.connected = False
            print(f"监控结束，共收到 {self.message_count} 条消息")
    
    def is_music_command(self, data):
        """检查是否是音乐相关指令"""
        if not isinstance(data, dict):
            return False
            
        # 检查是否是命令类型
        if data.get("type") == "command":
            cmd_data = data.get("data", {})
            cmd = cmd_data.get("cmd", "")
            return cmd in ["play_music", "music_ready", "music_error"]
        
        return False
    
    async def handle_music_command(self, data):
        """处理音乐指令"""
        cmd_data = data.get("data", {})
        cmd = cmd_data.get("cmd", "")
        
        if cmd == "play_music":
            print("📤 ESP32发送了音乐播放请求")
            print("等待服务器响应...")
            
        elif cmd == "music_ready":
            print("📥 服务器确认音乐准备就绪")
            print("应该很快收到音频数据...")
            
        elif cmd == "music_error":
            print("❌ 服务器报告音乐播放错误")
            error_msg = cmd_data.get("message", "未知错误")
            print(f"错误信息: {error_msg}")
    
    async def send_test_music_command(self):
        """发送测试音乐指令"""
        if not self.connected:
            print("未连接到服务器")
            return
            
        test_command = {
            "type": "command",
            "data": {
                "cmd": "play_music",
                "action": "start",
                "sessionId": str(int(time.time())),
                "timestamp": int(time.time())
            }
        }
        
        print("发送测试音乐指令...")
        print(json.dumps(test_command, indent=2))
        
        await self.websocket.send(json.dumps(test_command))
        print("✓ 测试指令已发送")

async def main():
    """主函数"""
    print("WebSocket连接调试工具")
    print("用于诊断ESP32与服务器的连接问题")
    print("=" * 50)
    
    # 检查命令行参数
    server_url = "ws://192.168.3.17:8768"
    if len(sys.argv) > 1:
        server_url = sys.argv[1]
    
    print(f"目标服务器: {server_url}")
    print("请确保WebSocket音频服务器正在运行")
    print()
    
    debugger = WebSocketDebugger(server_url)
    
    try:
        # 启动监控任务
        monitor_task = asyncio.create_task(debugger.connect_and_monitor())
        
        # 等待连接建立
        await asyncio.sleep(2)
        
        if debugger.connected:
            print("\n可用命令:")
            print("  输入 'test' 发送测试音乐指令")
            print("  输入 'quit' 退出")
            print("  直接按回车继续监控")
            print()
            
            # 简单的命令行交互
            while debugger.connected:
                try:
                    # 非阻塞输入检查
                    await asyncio.sleep(1)
                    
                    # 这里可以添加更多交互功能
                    # 由于asyncio的限制，简化为纯监控模式
                    
                except KeyboardInterrupt:
                    print("\n用户中断，退出监控")
                    break
        
        # 等待监控任务完成
        await monitor_task
        
    except KeyboardInterrupt:
        print("\n监控被用户中断")
    except Exception as e:
        print(f"运行时错误: {e}")

if __name__ == "__main__":
    # 检查依赖
    try:
        import websockets
    except ImportError:
        print("错误: 缺少websockets库")
        print("请运行: pip install websockets")
        sys.exit(1)
    
    # 显示使用说明
    if len(sys.argv) > 2 or (len(sys.argv) == 2 and sys.argv[1] in ["-h", "--help"]):
        print("使用方法:")
        print("  python3 debug_websocket_connection.py [WebSocket服务器URL]")
        print("")
        print("示例:")
        print("  python3 debug_websocket_connection.py")
        print("  python3 debug_websocket_connection.py ws://*************:8768")
        print("")
        sys.exit(0)
    
    # 运行调试器
    asyncio.run(main())
