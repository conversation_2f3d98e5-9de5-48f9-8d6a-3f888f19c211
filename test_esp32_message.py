#!/usr/bin/env python3
"""
ESP32消息测试工具
发送与ESP32完全相同的JSON消息来测试服务器响应
"""

import asyncio
import websockets
import json
import time

async def test_esp32_message():
    """测试ESP32发送的确切消息"""
    
    server_url = "ws://192.168.3.17:8768"
    
    try:
        print("连接到服务器...")
        websocket = await websockets.connect(server_url)
        print("✅ 连接成功")
        
        # 等待欢迎消息
        print("等待欢迎消息...")
        welcome_msg = await websocket.recv()
        print(f"收到欢迎消息: {welcome_msg}")
        
        # 发送与ESP32完全相同的JSON消息
        esp32_message = {
            "type": "command",
            "data": {
                "cmd": "play_music",
                "action": "start", 
                "sessionId": "33471",
                "timestamp": 33471
            }
        }
        
        print(f"\n发送ESP32格式的消息:")
        print(json.dumps(esp32_message, indent=2))
        
        await websocket.send(json.dumps(esp32_message))
        print("✅ 消息已发送")
        
        # 等待响应
        print("等待服务器响应...")
        
        try:
            response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
            print(f"收到响应: {response}")
            
            # 继续监听可能的音频数据
            print("监听音频数据...")
            for i in range(10):  # 监听10次
                try:
                    data = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                    if isinstance(data, bytes):
                        print(f"收到音频数据包 #{i+1}: {len(data)} 字节")
                    else:
                        print(f"收到文本消息: {data}")
                except asyncio.TimeoutError:
                    print(f"等待超时 #{i+1}")
                    
        except asyncio.TimeoutError:
            print("❌ 等待响应超时")
            
        await websocket.close()
        print("连接已关闭")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    print("ESP32消息测试工具")
    print("=" * 40)
    asyncio.run(test_esp32_message())
